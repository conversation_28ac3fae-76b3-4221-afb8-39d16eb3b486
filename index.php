<?php
require_once 'vendor/autoload.php';

use Dompdf\Dompdf;
use setasign\Fpdi\Fpdi;

// Order data (hardcoded for demo purposes)
$order = [
    'number' => '#958201',
    'billing_address' => [
        'companyname' => null,
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
        'phone' => '0101234567',
    ],
    'delivery_address' => [
        'companyname' => '',
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
    ],
    'order_lines' => [
        [
            'amount_ordered' => 2,
            'name' => 'Jeans - Black - 36',
            'sku' => 69205,
            'ean' => '8710552295268',
        ],
        [
            'amount_ordered' => 1,
            'name' => 'Sjaal - Rood Oranje',
            'sku' => 25920,
            'ean' => '3059943009097',
        ]
    ]
];

// API credentials
$user = '<EMAIL>';
$password = '4QJW9yh94PbTcpJGdKz6egwH';
$company = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brand = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';

function call_qls_api($endpoint, $method = 'GET', $data = null)
{
    global $user, $password;

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($curl, CURLOPT_USERPWD, $user . ':' . $password);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, false);

    if ($method === 'POST' && $data) {
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $redirect_url = curl_getinfo($curl, CURLINFO_REDIRECT_URL);
    $error = curl_error($curl);
    curl_close($curl);

    if ($response === false) {
        throw new Exception("API connection failed: " . $error);
    }

    // Handle 201 Created with Location header (successful shipment creation)
    if ($http_code === 201 && $redirect_url) {
        $parts = explode('/', $redirect_url);
        $shipment_id = end($parts);
        return ['success' => true, 'shipment_id' => $shipment_id];
    }

    if ($http_code >= 400) {
        $error_data = json_decode($response, true);
        if ($error_data && isset($error_data['errors'])) {
            $errors = [];
            foreach ($error_data['errors'] as $field => $messages) {
                if (is_array($messages)) {
                    $errors = array_merge($errors, $messages);
                } else {
                    $errors[] = $messages;
                }
            }
            throw new Exception("API validation errors: " . implode(', ', $errors));
        }
        throw new Exception("API request failed with status: $http_code");
    }

    return $response;
}

/**
 * Create a shipment via the QLS API.
 * @throws Exception
 */
function create_shipment($productId)
{
    global $order, $company, $brand;

    // Prepare shipment data
    $shipmentData = [
        'product_id' => (int)$productId,
        'product_combination_id' => 3, // DHL Pakje combination ID
        'brand_id' => $brand,
        'reference' => $order['number'],
        'weight' => 500, // Lighter weight 500g
        'cod_amount' => 0, // No COD
        'receiver_contact' => [
            'name' => $order['delivery_address']['name'],
            'companyname' => $order['delivery_address']['companyname'] ?: '',
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
            'email' => $order['billing_address']['email'], // Add required email
        ]
    ];

    // Call QLS API to create shipment using v2 endpoint
    $response = call_qls_api("/v2/companies/$company/shipments", 'POST', $shipmentData);

    // Handle redirect to shipment ID
    if (isset($response['redirect_url'])) {
        $parts = explode('/', $response['redirect_url']);
        $shipment_id = end($parts);
        return ['id' => $shipment_id];
    }

    // Check for errors in response
    $result = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Invalid JSON response from API. Raw response: " . substr($response, 0, 200));
    }

    if (isset($result['errors'])) {
        $error_messages = [];
        foreach ($result['errors'] as $error_group) {
            if (is_array($error_group)) {
                foreach ($error_group as $error_message) {
                    $error_messages[] = $error_message;
                }
            } else {
                $error_messages[] = $error_group;
            }
        }
        $errors = implode(', ', $error_messages);
        throw new Exception("API error: " . $errors);
    }

    // Debug: show raw response if no data
    if (!isset($result['data'])) {
        throw new Exception("No data in API response. Raw response: " . substr($response, 0, 500));
    }

    if (!isset($result['data']['id'])) {
        throw new Exception("Invalid response from API - missing shipment ID");
    }

    return $result['data'];
}

/**
 * Retrieve shipping label PDF from QLS.
 */
function get_shipping_label($shipment_id): ?string
{
    global $company;

    if (empty($shipment_id)) {
        return null;
    }

    try {
        return call_qls_api("/v2/companies/$company/shipments/$shipment_id/label");
    } catch (Exception $e) {
        return null;
    }
}

/**
 * Generate PDF document with invoice and shipping label.
 */
function build_pdf_document($label): ?string
{
    global $order;

    if (!isset($order['billing_address'], $order['delivery_address'])) {
        return null;
    }

    // Prepare the HTML content for the invoice
    $billing = $order['billing_address'];
    $delivery = $order['delivery_address'];
    $order_ref = $order['number'];

    $css = file_exists('style.css') ? file_get_contents('style.css') : '';
    $html = <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>$css</style>
</head>
<body>
    <div class="document-header">
        <h1>INVOICE - $order_ref</h1>
    </div>

    <div class="address-container">
        <div class="addr-left">
            <h3>Billing Address</h3>
            <p>$billing[name]</p>
            <p>$billing[street] $billing[housenumber]</p>
            <p>$billing[zipcode] $billing[city]</p>
            <p>$billing[email]</p>
        </div>
        <div class="addr-right">
            <h3>Delivery Address</h3>
            <p>$delivery[name]</p>
            <p>$delivery[street] $delivery[housenumber]</p>
            <p>$delivery[zipcode] $delivery[city]</p>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>Qty</th>
                <th>Item</th>
                <th>SKU</th>
                <th>EAN</th>
            </tr>
        </thead>
        <tbody>
HTML;

    // Loop through order items
    foreach ($order['order_lines'] as $item) {
        $html .= "<tr>
            <td>{$item['amount_ordered']}</td>
            <td>{$item['name']}</td>
            <td>{$item['sku']}</td>
            <td>{$item['ean']}</td>
        </tr>";
    }

    $html .= "</tbody></table></body></html>";

    // Generate PDF from the HTML
    $dompdf = new Dompdf();
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4');
    $dompdf->render();
    $pdf = $dompdf->output();

    // If a shipping label exists, combine it with the invoice PDF
    if ($label) {
        try {
            $combined = new Fpdi();

            // Add invoice pages
            $temp_invoice = tempnam(sys_get_temp_dir(), 'invoice_');
            file_put_contents($temp_invoice, $pdf);
            $combined->setSourceFile($temp_invoice);

            // Import pages from the invoice PDF
            for ($i = 1; $i <= $combined->getNumPages(); $i++) {
                $combined->AddPage();
                $tpl = $combined->importPage($i);
                $combined->useTemplate($tpl);
            }

            // Add label pages
            $temp_label = tempnam(sys_get_temp_dir(), 'label_');
            file_put_contents($temp_label, $label);
            $combined->setSourceFile($temp_label);

            // Import pages from the label PDF
            for ($i = 1; $i <= $combined->getNumPages(); $i++) {
                $combined->AddPage();
                $tpl = $combined->importPage($i);
                $combined->useTemplate($tpl);
            }

            // Output the combined PDF
            ob_start();
            $combined->Output('combined.pdf', 'I');
            $combined_pdf = ob_get_clean();

            // Cleanup temp files
            unlink($temp_invoice);
            unlink($temp_label);

            return $combined_pdf;
        } catch (Exception $e) {
            // If combining the label fails, return the invoice PDF
            return $pdf;
        }
    }

    return $pdf;
}

// Handle form submission
$error_message = '';
if ($_POST) {
    $product_id = isset($_POST['product_id']) && is_numeric($_POST['product_id']) ? (int)$_POST['product_id'] : 3;

    try {
        $shipment = create_shipment($product_id);

        if (!$shipment || !isset($shipment['id'])) {
            $error_message = 'Failed to create shipment. Please try again.';
        } else {
            $label = get_shipping_label($shipment['id']);
            $pdf = build_pdf_document($label);

            if (!$pdf) {
                $error_message = 'Failed to generate PDF. Please try again.';
            } else {
                // Create a filename for the PDF
                $order_ref = preg_replace('/[^a-zA-Z0-9]/', '', $order['number']);
                $timestamp = date('Y-m-d_H-i-s');
                $filename = "invoice_{$order_ref}_{$timestamp}.pdf";

                // Send the generated PDF to the browser
                header('Content-Type: application/pdf');
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                echo $pdf;
                exit;
            }
        }
    } catch (Exception $e) {
        $error_message = 'Error: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>QLS Shipping Label Generator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<h1>QLS Shipping Label Generator</h1>

<div class="main-container">
    <h2>Order <?= htmlspecialchars($order['number']) ?></h2>

    <div class="order-info">
        <p><strong>Customer:</strong> <?= htmlspecialchars($order['delivery_address']['name']) ?></p>
        <p><strong>Delivery Address:</strong>
            <?= htmlspecialchars($order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber']) ?>
            ,
            <?= htmlspecialchars($order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city']) ?>
        </p>
    </div>

    <table>
        <thead>
        <tr>
            <th>Qty</th>
            <th>Item</th>
            <th>SKU</th>
            <th>EAN</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($order['order_lines'] as $item): ?>
            <tr>
                <td><?= (int)$item['amount_ordered'] ?></td>
                <td><?= htmlspecialchars($item['name']) ?></td>
                <td><?= htmlspecialchars($item['sku']) ?></td>
                <td><?= htmlspecialchars($item['ean']) ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

<div class="main-container">
    <h2>Generate Shipping Label</h2>
    <p>Shipping via DHL Package. Click the button to create a PDF with invoice and shipping label.</p>

    <?php if ($error_message): ?>
        <div id="status-message"><?= htmlspecialchars($error_message) ?></div>
    <?php endif; ?>

    <form method="post">
        <input type="hidden" name="product_id" value="3">
        <button type="submit">Generate Invoice + Label</button>
    </form>
</div>
</body>
</html>
