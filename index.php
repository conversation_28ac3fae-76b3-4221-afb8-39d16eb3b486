<?php
require_once 'vendor/autoload.php';

use Dompdf\Dompdf;

// Order data
$order = [
    'number' => '#958201',
    'billing_address' => [
        'companyname' => null,
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
        'phone' => '0101234567',
    ],
    'delivery_address' => [
        'companyname' => '',
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
    ],
    'order_lines' => [
        [
            'amount_ordered' => 2,
            'name' => 'Jeans - Black - 36',
            'sku' => 69205,
            'ean' => '8710552295268',
        ],
        [
            'amount_ordered' => 1,
            'name' => 'Sjaal - Rood Oranje',
            'sku' => 25920,
            'ean' => '3059943009097',
        ]
    ]
];

// QLS API credentials
$user = '<EMAIL>';
$password = '4QJW9yh94PbTcpJGdKz6egwH';
$companyId = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brandId = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';

// wrap all API calls into a function
function call_qls_api($endpoint, $method = 'GET', $payload = null)
{
    global $user, $password;

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($curl, CURLOPT_USERPWD, $user . ':' . $password);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);

    if ($method === 'POST' && $payload) {
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($payload));
    }

    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $redirect_url = curl_getinfo($curl, CURLINFO_REDIRECT_URL);
    curl_close($curl);

    // basic error handling
    if ($response === false || $http_code >= 400) {
        throw new RuntimeException("QLS API call failed. HTTP code: $http_code, Response: " . ($response ?: 'false'));
    }

    return $response;
}


function create_shipment($productCombinationId)
{
    global $order, $companyId, $brandId;

    // build the shipment information for the API
    $shipment_data = array(
        'product_id' => (int)$productCombinationId,
        'product_combination_id' => (int)$productCombinationId,
        'brand_id' => $brandId,
        'reference' => $order['number'],
        'weight' => 250, // 250g weight
        'cod_amount' => 0, // no cash on delivery
        'receiver_contact' => array(
            'name' => $order['delivery_address']['name'],
            'companyname' => $order['delivery_address']['companyname'] ?: '',
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
        )
    );

    $response = call_qls_api("/companies/$companyId/shipments", 'POST', $shipment_data);

    $result = json_decode($response, true);

    if (!$result || !isset($result['id'])) {
        throw new RuntimeException("Failed to create shipment");
    }

    return $result;
}

function get_shipping_label($shipment_id)
{
    global $companyId;

    $response = call_qls_api("/companies/$companyId/shipments/$shipment_id/label");

    if (!$response) {
        throw new RuntimeException("Failed to get shipping label");
    }

    return $response;
}

function generate_pakbon_pdf()
{
    global $order;
    $billing = $order['billing_address'];
    $delivery = $order['delivery_address'];
    $order_num = $order['number'];

    // load CSS and build HTML for PDF
    $css_content = file_get_contents('style.css');
    $html_content = '<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <title>QLS Verzendlabel Generator</title>
    <style>' . $css_content . '</style>
</head>
<body>
    <div class="document-header">
        <h1>PAKBON - ' . htmlspecialchars($order_num) . '</h1>
    </div>

    <div class="address-container">
        <div class="addr-left">
            <div class="address-box">
                <h3>Factuuradres</h3>
                ' . htmlspecialchars($billing['name']) . '<br>
                ' . htmlspecialchars($billing['street'] . ' ' . $billing['housenumber']) . '<br>
                ' . htmlspecialchars($billing['zipcode'] . ' ' . $billing['city']) . '<br>
                ' . htmlspecialchars($billing['email']) . '
            </div>
        </div>
        <div class="addr-right">
            <div class="address-box">
                <h3>Bezorgadres</h3>
                ' . htmlspecialchars($delivery['name']) . '<br>
                ' . htmlspecialchars($delivery['street'] . ' ' . $delivery['housenumber']) . '<br>
                ' . htmlspecialchars($delivery['zipcode'] . ' ' . $delivery['city']) . '
            </div>
        </div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Aantal</th>
                <th>Artikel</th>
                <th>SKU</th>
                <th>EAN</th>
            </tr>
        </thead>
        <tbody>';

    // Adds all the order items to the PDF
    foreach ($order['items'] as $item) {
        $html_content .= '<tr>
            <td>' . (int)$item['amount_ordered'] . '</td>
            <td>' . htmlspecialchars($item['name']) . '</td>
            <td>' . htmlspecialchars($item['sku']) . '</td>
            <td>' . htmlspecialchars($item['ean']) . '</td>
        </tr>';
    }

    $html_content .= '
        </tbody>
    </table>


</body>
</html>';

    // Generate the pdf
    $dompdf = new Dompdf();
    $dompdf->loadHtml($html_content);
    $dompdf->setPaper('A4');
    $dompdf->render();

    return $dompdf->output();
}

function combine_pakbon_and_label($pakbon_pdf, $label_pdf)
{
    $fpdi = new Fpdi();

    // Add pakbon
    $temp_pakbon = tempnam(sys_get_temp_dir(), 'pakbon_');
    file_put_contents($temp_pakbon, $pakbon_pdf);
    $page_count = $fpdi->setSourceFile($temp_pakbon);

    for ($i = 1; $i <= $page_count; $i++) {
        $fpdi->AddPage();
        $template = $fpdi->importPage($i);
        $fpdi->useTemplate($template);
    }

    // Add label
    $temp_label = tempnam(sys_get_temp_dir(), 'label_');
    file_put_contents($temp_label, $label_pdf);
    $page_count = $fpdi->setSourceFile($temp_label);

    for ($i = 1; $i <= $page_count; $i++) {
        $fpdi->AddPage();
        $template = $fpdi->importPage($i);
        $fpdi->useTemplate($template);
    }

    $combined = $fpdi->Output('S');

    unlink($temp_pakbon);
    unlink($temp_label);

    return $combined;
}

// checks if the form was submitted or not
$error_message = '';
if ($_POST) {
    try {
        // Create shipment
        $shipment = create_shipment(1);

        // Get shipping label PDF
        $label_pdf = get_shipping_label($shipment['id']);

        // Generate pakbon PDF
        $pakbon_pdf = generate_pakbon_pdf();

        // Combine both PDFs
        $combined_pdf = combine_pakbon_and_label($pakbon_pdf, $label_pdf);

        // Send to browser
        $orderRef = str_replace('#', '', $order['number']);
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "pakbon_label_{$orderRef}_{$timestamp}.pdf";

        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        echo $combined_pdf;
        exit;

    } catch (RuntimeException $e) {
        $error_message = 'Kon geen verzendlabel aanmaken. Probeer het later opnieuw.';
    }
}
?>
<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <title>Verzendlabel generator</title>
</head>
<body>

<form method="post">
    <label for="order_id">Order ID:</label>
    <input type="text" name="order_id" id="order_id" required>
    <button type="submit">Genereer label</button>
</form>

<?php if ($error): ?>
    <p style="color: red"><?= htmlspecialchars($error) ?></p>
<?php endif; ?>

<?php if ($shipment): ?>
    <p>Verzendlabel succesvol aangemaakt!</p>
    <p><strong>Trackingcode:</strong> <?= htmlspecialchars($shipment['tracking_code'] ?? 'Onbekend') ?></p>
    <p>
        <a href="data:application/pdf;base64,<?= base64_encode($pdf) ?>" download="verzendlabel.pdf">
            Download verzendlabel
        </a>
    </p>
<?php endif; ?>

</body>
</html>
