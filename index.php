<?php
require_once 'vendor/autoload.php';

use Dompdf\Dompdf;
use setasign\Fpdi\Fpdi;

// Order data (hardcoded for demo purposes)
$order = [
    'number' => '#958201',
    'billing_address' => [
        'companyname' => null,
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
        'phone' => '0101234567',
    ],
    'delivery_address' => [
        'companyname' => '',
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
    ],
    'order_lines' => [
        [
            'amount_ordered' => 2,
            'name' => 'Jeans - Black - 36',
            'sku' => 69205,
            'ean' => '8710552295268',
        ],
        [
            'amount_ordered' => 1,
            'name' => 'Sjaal - Rood Oranje',
            'sku' => 25920,
            'ean' => '3059943009097',
        ]
    ]
];

// API credentials
$user = '<EMAIL>';
$password = '4QJW9yh94PbTcpJGdKz6egwH';
$company = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brand = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';

function call_qls_api($endpoint, $method = 'GET', $data = null)
{
    global $user, $password;

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($curl, CURLOPT_USERPWD, $user . ':' . $password);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, false);

    if ($method === 'POST' && $data) {
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $redirect_url = curl_getinfo($curl, CURLINFO_REDIRECT_URL);
    $error = curl_error($curl);
    curl_close($curl);

    if ($response === false) {
        throw new Exception("API connection failed: " . $error);
    }

    // Handle 201 Created with Location header (successful shipment creation)
    if ($http_code === 201) {
        if ($redirect_url) {
            $parts = explode('/', $redirect_url);
            $shipment_id = end($parts);
            return ['success' => true, 'shipment_id' => $shipment_id];
        }
        // Sometimes the response body contains the shipment data
        $result = json_decode($response, true);
        if ($result && isset($result['data']['id'])) {
            return ['success' => true, 'shipment_id' => $result['data']['id']];
        }
    }

    if ($http_code >= 400) {
        $error_data = json_decode($response, true);
        if ($error_data && isset($error_data['errors'])) {
            $errors = [];
            foreach ($error_data['errors'] as $field => $messages) {
                if (is_array($messages)) {
                    foreach ($messages as $message) {
                        $errors[] = $message;
                    }
                } else {
                    $errors[] = $messages;
                }
            }
            throw new Exception("API validation errors: " . implode(', ', $errors));
        }
        throw new Exception("API request failed with status: $http_code. Response: " . substr($response, 0, 200));
    }

    return $response;
}

function create_shipment($product_id)
{
    global $order, $company, $brand;

    $shipment_data = [
        'product_id' => (int)$product_id,
        'product_combination_id' => 3,
        'brand_id' => $brand,
        'reference' => $order['number'],
        'weight' => 500,
        'cod_amount' => 0,
        'receiver_contact' => [
            'name' => $order['delivery_address']['name'],
            'companyname' => $order['delivery_address']['companyname'] ?: '',
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
            'email' => $order['delivery_address']['email'],
        ]
    ];

    // Use the correct QLS API endpoint from documentation
    $response = call_qls_api("/companies/$company/shipments", 'POST', $shipment_data);

    if (isset($response['success']) && $response['shipment_id']) {
        return $response['shipment_id'];
    }

    // Debug output for troubleshooting
    $debug_info = "Response type: " . gettype($response);
    if (is_array($response)) {
        $debug_info .= ", Keys: " . implode(', ', array_keys($response));
    } else {
        $debug_info .= ", Content: " . substr($response, 0, 200);
    }

    throw new Exception("Failed to create shipment. " . $debug_info);
}

function get_shipping_label($shipment_id)
{
    global $company;

    if (empty($shipment_id)) {
        throw new Exception("No shipment ID provided");
    }

    $response = call_qls_api("/v2/companies/$company/shipments/$shipment_id/label");

    if (empty($response)) {
        throw new Exception("Empty label response from API");
    }

    return $response;
}

function generate_pakbon_pdf()
{
    global $order;

    $billing = $order['billing_address'];
    $delivery = $order['delivery_address'];
    $order_ref = $order['number'];

    $html = <<<HTML
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; font-size: 12px; margin: 0; padding: 15px; }
        .header { text-align: center; margin-bottom: 20px; }
        .addresses { display: table; width: 100%; margin-bottom: 20px; }
        .address { display: table-cell; width: 48%; vertical-align: top; padding: 10px; border: 1px solid #ccc; }
        .address h3 { margin: 0 0 10px 0; font-size: 14px; }
        .address p { margin: 2px 0; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { border: 1px solid #999; padding: 6px; text-align: left; font-size: 11px; }
        th { background-color: #f0f0f0; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>PAKBON - $order_ref</h1>
    </div>

    <div class="addresses">
        <div class="address">
            <h3>Factuuradres</h3>
            <p>$billing[name]</p>
            <p>$billing[street] $billing[housenumber]</p>
            <p>$billing[zipcode] $billing[city]</p>
            <p>$billing[email]</p>
        </div>
        <div class="address">
            <h3>Bezorgadres</h3>
            <p>$delivery[name]</p>
            <p>$delivery[street] $delivery[housenumber]</p>
            <p>$delivery[zipcode] $delivery[city]</p>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>Aantal</th>
                <th>Artikel</th>
                <th>SKU</th>
                <th>EAN</th>
            </tr>
        </thead>
        <tbody>
HTML;

    foreach ($order['order_lines'] as $item) {
        $html .= "<tr>
            <td>{$item['amount_ordered']}</td>
            <td>" . htmlspecialchars($item['name']) . "</td>
            <td>{$item['sku']}</td>
            <td>{$item['ean']}</td>
        </tr>";
    }

    $html .= "</tbody></table></body></html>";

    $dompdf = new Dompdf();
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();

    return $dompdf->output();
}

function combine_pakbon_and_label($pakbon_pdf, $label_pdf)
{
    try {
        $fpdi = new Fpdi();

        // Add pakbon
        $temp_pakbon = tempnam(sys_get_temp_dir(), 'pakbon_');
        file_put_contents($temp_pakbon, $pakbon_pdf);
        $fpdi->setSourceFile($temp_pakbon);

        $page_count = $fpdi->setSourceFile($temp_pakbon);
        for ($i = 1; $i <= $page_count; $i++) {
            $fpdi->AddPage();
            $template = $fpdi->importPage($i);
            $fpdi->useTemplate($template);
        }

        // Add label
        $temp_label = tempnam(sys_get_temp_dir(), 'label_');
        file_put_contents($temp_label, $label_pdf);

        $page_count = $fpdi->setSourceFile($temp_label);
        for ($i = 1; $i <= $page_count; $i++) {
            $fpdi->AddPage();
            $template = $fpdi->importPage($i);
            $fpdi->useTemplate($template);
        }

        $combined_pdf = $fpdi->Output('S');

        // Cleanup
        unlink($temp_pakbon);
        unlink($temp_label);

        return $combined_pdf;

    } catch (Exception $e) {
        throw new Exception("Failed to combine PDFs: " . $e->getMessage());
    }
}

$error_message = '';
$success_message = '';

if ($_POST) {
    $product_id = isset($_POST['product_id']) && is_numeric($_POST['product_id']) ? (int)$_POST['product_id'] : 3;

    try {
        // Create shipment
        $shipment_id = create_shipment($product_id);

        // Get shipping label
        $label_pdf = get_shipping_label($shipment_id);

        // Generate pakbon
        $pakbon_pdf = generate_pakbon_pdf();

        // Combine both PDFs
        $combined_pdf = combine_pakbon_and_label($pakbon_pdf, $label_pdf);

        // Send to browser
        $order_ref = preg_replace('/[^a-zA-Z0-9]/', '', $order['number']);
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "pakbon_label_{$order_ref}_{$timestamp}.pdf";

        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . strlen($combined_pdf));
        echo $combined_pdf;
        exit;

    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <title>QLS Verzendlabel Generator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<div class="container">
    <h1>QLS Verzendlabel Generator</h1>
    <p class="subtitle">Combineer pakbon met verzendlabel in één document</p>

    <div class="order-section">
        <h2>Bestelling <?= htmlspecialchars($order['number']) ?></h2>

        <div class="order-details">
            <div class="customer-info">
                <h3>Klantgegevens</h3>
                <p><strong><?= htmlspecialchars($order['delivery_address']['name']) ?></strong></p>
                <p><?= htmlspecialchars($order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber']) ?></p>
                <p><?= htmlspecialchars($order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city']) ?></p>
            </div>

            <div class="order-items">
                <h3>Artikelen</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Aantal</th>
                            <th>Artikel</th>
                            <th>SKU</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($order['order_lines'] as $item): ?>
                            <tr>
                                <td><?= (int)$item['amount_ordered'] ?></td>
                                <td><?= htmlspecialchars($item['name']) ?></td>
                                <td><?= htmlspecialchars($item['sku']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="action-section">
        <h2>Verzendlabel Genereren</h2>
        <p>Klik op de knop om een PDF te maken met pakbon en DHL verzendlabel.</p>

        <?php if ($error_message): ?>
            <div class="error-message">
                <strong>Fout:</strong> <?= htmlspecialchars($error_message) ?>
            </div>
        <?php endif; ?>

        <form method="post" class="generate-form">
            <input type="hidden" name="product_id" value="3">
            <button type="submit" class="generate-btn">
                📦 Genereer Pakbon + Verzendlabel
            </button>
        </form>

        <div class="info-box">
            <h4>Hoe werkt het?</h4>
            <ul>
                <li>✅ Automatisch verzendlabel aanmaken via QLS API</li>
                <li>✅ Pakbon met bestelling informatie genereren</li>
                <li>✅ Beide documenten combineren in één PDF</li>
                <li>✅ Klaar voor printen op A4 formaat</li>
            </ul>
        </div>
    </div>
</div>
</body>
</html>
