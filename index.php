<?php
require_once 'vendor/autoload.php';

use Dompdf\Dompdf;

// Order data
$order = [
    'number' => '#958201',
    'billing_address' => [
        'companyname' => null,
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
        'phone' => '0101234567',
    ],
    'delivery_address' => [
        'companyname' => '',
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
    ],
    'order_lines' => [
        [
            'amount_ordered' => 2,
            'name' => 'Jeans - Black - 36',
            'sku' => 69205,
            'ean' => '8710552295268',
        ],
        [
            'amount_ordered' => 1,
            'name' => 'Sjaal - Rood Oranje',
            'sku' => 25920,
            'ean' => '3059943009097',
        ]
    ]
];

// QLS API credentials
$user = '<EMAIL>';
$password = '4QJW9yh94PbTcpJGdKz6egwH';
$companyId = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brandId = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';

// wrap all API calls into a function
function callApi($endpoint, $method = 'GET', $data = null) {
    global $user, $password;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, "$user:$password");

    if ($method == 'POST' && $data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($ch);
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($response === false || $statusCode >= 400) {
        throw new RuntimeException("API call failed. HTTP code: $statusCode");
    }

    return json_decode($response, true);
}

// Create shipment
function createShipment($productId) {
    global $order, $companyId, $brandId;
    $shipment = [
        'product_id' => $productId,
        'product_combination_id' => $productId,
        'brand_id' => $brandId,
        'reference' => $order['number'],
        'weight' => 250,
        'cod_amount' => 0,
        'receiver_contact' => [
            'name' => $order['delivery_address']['name'],
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
        ]
    ];
    return callApi("/companies/$companyId/shipments", 'POST', $shipment);
}

// Generate label HTML
function generateLabelHtml($shipmentId) {
    global $order;
    $trackingCode = 'QLS' . substr($shipmentId, -8);
    return "
        <div class='shipping-label'>
            <h3>Verzendlabel</h3>
            <p>Zending: $shipmentId</p>
            <p><strong>Bezorgen aan:</strong><br>" . htmlspecialchars($order['delivery_address']['name']) . "<br>
            " . htmlspecialchars($order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber']) . "<br>
            " . htmlspecialchars($order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city']) . "</p>
            <div class='barcode-area'>
                <div class='barcode-display'>|||| ||| |||| ||||</div>
                <p>Track & Trace: $trackingCode</p>
            </div>
        </div>";
}

// Build PDF
function buildPdf($labelHtml) {
    global $order;
    $css = file_get_contents('style.css');
    $html = "
    <html>
        <head><style>$css</style></head>
        <body>
            <h1>PAKBON - {$order['number']}</h1>
            <div class='addresses'>
                <div>Factuuradres: {$order['billing_address']['name']}</div>
                <div>Bezorgadres: {$order['delivery_address']['name']}</div>
            </div>
            <table>
                <tr><th>Aantal</th><th>Artikel</th><th>SKU</th><th>EAN</th></tr>";
    foreach ($order['order_lines'] as $item) {
        $html .= "<tr><td>{$item['amount_ordered']}</td><td>{$item['name']}</td><td>{$item['sku']}</td><td>{$item['ean']}</td></tr>";
    }
    $html .= "</table>
        <div>$labelHtml</div>
        </body>
    </html>";

    $dompdf = new Dompdf();
    $dompdf->loadHtml($html);
    $dompdf->render();
    return $dompdf->output();
}

// Handle form submission
if ($_POST) {
    try {
        $shipmentResult = createShipment(1);
        $labelHtml = generateLabelHtml($shipmentResult['id']);
        $pdfContent = buildPdf($labelHtml);

        $orderRef = str_replace('#', '', $order['number']);
        $timestamp = date('Y-m-d_H-i-s');
        $pdfFilename = "pakbon_{$orderRef}_{$timestamp}.pdf";

        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $pdfFilename . '"');
        echo $pdfContent;
        exit;
    } catch (RuntimeException $e) {
        $errorMessage = 'Error generating shipping label. Please try again later.';
    }
}
?>

<!DOCTYPE html>
<html lang="nl">
<head><meta charset="UTF-8"><title>Verzendlabel Generator</title></head>
<body>
<form method="post">
    <label for="order_id">Order ID:</label>
    <input type="text" name="order_id" id="order_id" required>
    <button type="submit">Genereer label</button>
</form>

<?php if (!empty($errorMessage)): ?>
    <p style="color: red"><?= htmlspecialchars($errorMessage) ?></p>
<?php endif; ?>
</body>
</html>
