body {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
    background: #f5f5f5;
    color: #333;
}

.container {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    text-align: center;
    color: #2c5aa0;
    margin-bottom: 5px;
}

.subtitle {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
    font-style: italic;
}

.order-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 25px;
    border-left: 4px solid #2c5aa0;
}

.order-details {
    display: flex;
    gap: 30px;
    margin-top: 15px;
}

.customer-info {
    flex: 1;
}

.order-items {
    flex: 2;
}

h2 {
    color: #2c5aa0;
    margin-bottom: 15px;
}

h3 {
    color: #555;
    margin-bottom: 10px;
    font-size: 16px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f0f0f0;
    font-weight: bold;
}
/* PDF styling */
.document-header {
    margin-bottom: 20px;
}

.address-container {
    margin-bottom: 20px;
}

.addr-left {
    display: inline-block;
    width: 48%;
    vertical-align: top;
}

.addr-right {
    display: inline-block;
    width: 48%;
    vertical-align: top;
    margin-left: 2%;
}

.address-box {
    border: 1px solid #999;
    padding: 10px;
}

.items-table {
    width: 100%;
    margin: 15px 0;
}

#status-message {
    background: #ffebee;
    color: #c62828;
    padding: 10px;
    margin: 10px 0;
    border: 1px solid #ffcdd2;
}